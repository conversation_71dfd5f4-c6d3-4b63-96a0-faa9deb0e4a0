<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<style>
    /* Form styling for better spacing and responsiveness */
    .form-group {
        margin-bottom: 10px; /* Further reduced from 15px */
    }
    
    .form-control {
        height: auto;
        padding: 8px; /* Reduced from 10px */
        border-radius: 4px;
        border: 1px solid #ddd;
    }
    
    .form-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    select.form-control {
        padding: 7px 8px; /* Reduced from 9px 10px */
        height: 34px; /* Further reduced from 38px */
    }
    
    textarea.form-control {
        min-height: 70px; /* Further reduced from 80px */
    }
    
    /* Restore glyphicon color */
    .glyphicon {
        color: #000; /* Black color for glyphicons */
    }
    
    .input-group-addon {
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-left: none;
        padding: 6px 10px; /* Reduced from 8px 12px */
        border-radius: 0 4px 4px 0;
        color: #000; /* Ensure text/icon color is black */
    }
    
    label {
        font-weight: 600;
        margin-bottom: 5px; /* Further reduced from 8px */
        display: block;
    }
    
    .card {
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .card-header {
        padding: 15px 20px;
    }
    
    .card-body {
        /* padding: 30px; Increased padding */
    }
    
    .btn {
        padding: 8px 16px; /* Reduced from 10px 20px */
        font-weight: 600;
        border-radius: 4px;
    }
    
    .btn-primary {
        background-color: #007bff;
        border-color: #007bff;
    }
    
    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
    }
    
    .alert {
        padding: 12px 15px;
        border-radius: 4px;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .card-body {
            padding: 15px;
        }
        
        .form-group {
            margin-bottom: 25px; /* Still increased but slightly less on mobile */
        }
        
        .row {
            margin-left: -10px;
            margin-right: -10px;
        }
        
        .col-md-6, .col-md-12 {
            padding-left: 10px;
            padding-right: 10px;
        }
        
        /* Mobile-specific help text styling */
        .help-block {
            font-size: 12px;
            margin-top: 8px;
            line-height: 1.4;
        }
        
        .help-block i {
            margin-right: 4px;
        }
        
        /* Adjust photo upload container for mobile */
        .photo-upload-container {
            flex-direction: column;
            gap: 15px;
        }

        /* Parent selection cards for mobile */
        .parent-card {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .parent-card.selected {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        
        .upload-details {
            width: 100%;
        }
    }
    
    /* Reduce space between rows */
    .row {
        margin-bottom: 5px; /* Further reduced from 10px */
    }
    
    /* Reduce space before the submit button */
    .mt-4 {
        margin-top: 1rem !important; /* Further reduced from 1.5rem */
    }
    
    /* Reduce space after the last form group */
    .form-group:last-child {
        margin-bottom: 15px; /* Further reduced from 20px */
    }
    
    /* Reduce padding in card body */
    .card-body {
        padding: 15px; /* Further reduced from 20px */
    }
    
    /* Reduce space between label and input */
    label {
        margin-bottom: 5px; /* Further reduced from 8px */
    }
    
    /* Adjust row inline spacing */
    .row[style="margin-bottom: 25px;"] {
        margin-bottom: 10px !important; /* Further reduced from 15px */
    }
    
    /* File input styling */
    input[type="file"].form-control {
        padding: 6px;
    }
    
    /* Improve parent info display */
    #parent_info_div {
        margin-top: 15px;
        padding: 15px;
        border-radius: 8px;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .parent-info-container {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .parent-photo {
        width: 100px;
        height: 100px;
        border-radius: 8px;
        object-fit: cover;
        border: 2px solid #dee2e6;
    }

    .parent-details {
        flex: 1;
    }

    .parent-name {
        font-size: 16px;
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    .parent-contact {
        font-size: 14px;
        color: #6c757d;
        margin-top: 5px;
    }

    .parent-label {
        font-size: 14px;
        color: #6c757d;
    }

    /* Parent selection card styles */
    .parent-selection-container {
        margin-top: 10px;
    }

    .parent-option {
        margin-bottom: 15px;
    }

    .parent-option input[type="radio"] {
        display: none;
    }

    .parent-card {
        display: block;
        padding: 15px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        background-color: #fff;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        color: inherit;
    }

    .parent-card:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
        text-decoration: none;
        color: inherit;
    }

    .parent-option input[type="radio"]:checked + .parent-card {
        border-color: #007bff;
        background-color: #e7f3ff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .parent-card .parent-info {
        text-align: center;
    }

    .parent-card .parent-name {
        font-size: 16px;
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    .parent-card .parent-mobile {
        font-size: 14px;
        color: #6c757d;
    }

    /* Disabled parent card styles */
    .parent-option input[type="radio"]:disabled + .parent-card {
        background-color: #f8f9fa;
        border-color: #dee2e6;
        cursor: not-allowed;
        opacity: 0.7;
    }

    .parent-option input[type="radio"]:disabled + .parent-card:hover {
        border-color: #dee2e6;
        background-color: #f8f9fa;
    }

    .parent-option input[type="radio"]:disabled + .parent-card .selection-checkbox {
        background-color: #e9ecef;
        border-color: #dee2e6;
    }

    /* Checkbox selection styles */
    .parent-card {
        position: relative;
    }

    .parent-card .selection-checkbox {
        position: absolute;
        top: 8px;
        left: 8px;
        width: 20px;
        height: 20px;
        border: 2px solid #ddd;
        border-radius: 4px;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .parent-card .selection-checkbox .checkmark {
        color: #fff;
        font-size: 12px;
        font-weight: bold;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .parent-option input[type="radio"]:checked + .parent-card .selection-checkbox {
        background-color: #007bff;
        border-color: #007bff;
    }

    .parent-option input[type="radio"]:checked + .parent-card .selection-checkbox .checkmark {
        opacity: 1;
    }

    /* Photo loading styles */
    .photo-container {
        position: relative;
        width: 100px;
        height: 100px;
    }

    .photo-loader {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
    }

    .photo-loader .spinner {
        width: 30px;
        height: 30px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .parent-photo {
        width: 100px;
        height: 100px;
        border-radius: 8px;
        object-fit: cover;
        border: 2px solid #dee2e6;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .parent-photo.loaded {
        opacity: 1;
    }

    /* Photo upload styles */
    .photo-upload-container {
        margin: 10px 0;
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .photo-preview-container {
        position: relative;
        width: 100px;
        height: 100px;
        border-radius: 8px;
        overflow: hidden;
        display: none;
        background: #f8f9fa;
        border: 2px solid #dee2e6;
    }

    .photo-preview {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .upload-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 5px;
        text-align: center;
        display: none;
    }

    .upload-progress-bar {
        height: 4px;
        background: #4CAF50;
        width: 0%;
        transition: width 0.3s ease;
    }

    .remove-photo {
        position: absolute;
        top: 5px;
        right: 5px;
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        cursor: pointer;
        display: none;
        font-size: 16px;
        line-height: 1;
        padding: 0;
    }

    .remove-photo:hover {
        background: rgba(255, 255, 255, 1);
    }

    .upload-details {
        flex: 1;
    }

    .custom-file-upload {
        display: inline-block;
        padding: 8px 16px;
        background: #007bff;
        color: white;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.3s ease;
        font-size: 14px;
    }

    .custom-file-upload:hover {
        background: #0056b3;
    }

    .custom-file-upload i {
        margin-right: 5px;
    }

    #pickup_photo {
        display: none;
    }
</style>

<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2');?>">Attendance</a></li>
    <li><a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2/emergency_exit');?>">Emergency Exit</a></li>
    <li>Create Emergency Exit</li>
</ul>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card cd_border">
                <div class="card-header panel_heading_new_style_staff_border">
                    <div class="row" style="margin: 0px">
                        <h3 class="card-title panel_title_new_style_staff">
                            <a class="back_anchor" href="<?php echo site_url('attendance_day_v2/Attendance_day_v2/emergency_exit') ?>">
                                <span class="fa fa-arrow-left"></span>
                            </a> 
                            Create Emergency Exit
                        </h3>
                    </div>
                </div>
                <div class="card-body">
                    <form action="<?php echo site_url('attendance_day_v2/Attendance_day_v2/save_emergency_exit'); ?>" method="post" enctype="multipart/form-data" id="emergency_exit_form" novalidate>
                        <div class="row justify-content-center">
                            <div class="col-md-10">
                                <div class="row" style="margin-bottom: 25px;">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Exit Date & Time <font color="red">*</font></label>
                                            <div class="input-group">
                                                <input type="text" id="exit_datetime" name="exit_datetime" class="form-control" value="<?php echo date('d-m-Y h:i A'); ?>" required>
                                                <span class="input-group-addon"><span class="glyphicon glyphicon-calendar"></span></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <!-- Placeholder for balance if needed -->
                                    </div>
                                </div>
                                
                                <div class="row" style="margin-bottom: 25px;">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Section <font color="red">*</font></label>
                                            <select id="class_section_id" name="class_section_id" class="form-control" required>
                                                <option value="">Select Section</option>
                                                <?php foreach ($class_section as $section): ?>
                                                <option value="<?= $section->sectionID ?>"><?= $section->class_name.$section->section_name ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Student <font color="red">*</font></label>
                                            <select id="student_id" name="student_id" class="form-control" required disabled>
                                                <option value="">Select Student</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row" style="margin-bottom: 25px;">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Pick-up By <font color="red">*</font></label>
                                            <select id="pickup_by" name="pickup_by" class="form-control" required>
                                                <option value="">Select</option>
                                                <option value="Self">Self (Student)</option>
                                                <option value="Father">Father</option>
                                                <option value="Mother">Mother</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Other Name and Photo Section (appears after Other is selected) -->
                                <div class="row" id="other_details_section" style="display:none; margin-bottom: 25px;">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Other Name <font color="red">*</font></label>
                                            <input type="text" placeholder="Enter other name" id="pickup_other_name" name="pickup_other_name" class="form-control" maxlength="50">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Pick-up Person Photo <font color="red">*</font></label>
                                            <div class="photo-upload-container">
                                                <div class="photo-preview-container" id="photo_preview_container">
                                                    <img id="photo_preview" class="photo-preview">
                                                    <div class="upload-progress" id="upload_progress">
                                                        <div class="upload-progress-bar" id="upload_progress_bar"></div>
                                                        <span id="upload_percentage">0%</span>
                                                    </div>
                                                    <button type="button" class="remove-photo" onclick="removePhoto()">×</button>
                                                </div>
                                                <div class="upload-details">
                                                    <label for="pickup_photo" class="custom-file-upload">
                                                        <i class="fa fa-camera"></i> Choose Photo
                                                    </label>
                                                    <input type="file" id="pickup_photo" name="pickup_photo" accept="image/*" onchange="handlePhotoUpload(this)">
                                                    <input type="hidden" id="uploaded_photo_url" name="uploaded_photo_url">
                                                    <small class="help-block">
                                                        <i class="fa fa-info-circle"></i> Maximum file size: 5MB. Supported formats: JPG, PNG, GIF
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>



                                <!-- OTP Selection and Verification Section (appears after pickup_by selection) -->
                                <div class="row" id="otp_complete_section" style="display:none; margin-bottom: 25px;">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label>Send OTP to <font color="red">*</font></label>
                                            <div class="parent-selection-container">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="parent-option" id="father_option" style="display:none;">
                                                            <input type="radio" id="otp_to_father" name="otp_to" value="Father">
                                                            <label for="otp_to_father" class="parent-card">
                                                                <div class="selection-checkbox">
                                                                    <span class="checkmark">✓</span>
                                                                </div>
                                                                <div class="parent-info">
                                                                    <div class="parent-name" id="father_name">Father</div>
                                                                    <div class="parent-mobile" id="father_mobile">Mobile not available</div>
                                                                </div>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="parent-option" id="mother_option" style="display:none;">
                                                            <input type="radio" id="otp_to_mother" name="otp_to" value="Mother">
                                                            <label for="otp_to_mother" class="parent-card">
                                                                <div class="selection-checkbox">
                                                                    <span class="checkmark">✓</span>
                                                                </div>
                                                                <div class="parent-info">
                                                                    <div class="parent-name" id="mother_name">Mother</div>
                                                                    <div class="parent-mobile" id="mother_mobile">Mobile not available</div>
                                                                </div>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <small class="help-block text-muted">
                                                <i class="fa fa-info-circle"></i>
                                                Select the parent to whom OTP should be sent for verification.
                                            </small>
                                        </div>
                                    </div>

                                    <!-- OTP Verification UI -->
                                    <div class="col-md-12" id="otp_verification_ui" style="display:none;">
                                        <div class="alert alert-info">
                                            <h5><i class="fa fa-shield"></i> OTP Verification Required</h5>
                                            <p>Please verify your identity with OTP before proceeding.</p>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6" id="otp_input_div" style="display:none;">
                                                <div class="form-group">
                                                    <label>Enter OTP <font color="red">*</font></label>
                                                    <input type="number" id="otp_code" name="otp_code" class="form-control" placeholder="Enter 6-digit OTP" maxlength="6">
                                                    <small class="help-block">
                                                        <i class="fa fa-info-circle"></i> OTP is valid for 10 minutes only
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="col-md-12 text-center">
                                                <button type="button" class="btn btn-info" id="send_otp_btn">Send OTP</button>
                                                <button type="button" class="btn btn-success" id="verify_otp_btn" style="display:none;">Verify OTP</button>
                                                <button type="button" class="btn btn-warning" id="resend_otp_btn" style="display:none;">Resend OTP</button>
                                                <button type="button" class="btn btn-secondary" id="cancel_otp_btn">Cancel</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Remarks Section (always visible) -->
                                <div class="row" style="margin-bottom: 25px;">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label>Remarks <font color="red">*</font></label>
                                            <textarea placeholder="Enter remarks" id="remarks" name="remarks" class="form-control" rows="3" required></textarea>
                                        </div>
                                    </div>
                                </div>


                                

                                
                                <!-- Submit Section (always visible but conditionally disabled) -->
                                <div class="row mt-4" id="submit_section">
                                    <div class="col-md-12 text-center">
                                        <button type="submit" class="btn btn-success" id="final_submit_btn" <?php echo $emergency_exit_otp_enabled ? 'disabled' : ''; ?>>Submit Emergency Exit</button>
                                        <a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2/emergency_exit'); ?>" class="btn btn-danger">Cancel</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // Emergency Exit OTP Configuration
    var otpEnabled = <?php echo $this->settings->getSetting('emergency_exit_otp_verification') ? 'true' : 'false'; ?>;

    // Set today's date in the correct format
    var today = new Date();
    var dd = String(today.getDate()).padStart(2, '0');
    var mm = String(today.getMonth() + 1).padStart(2, '0');
    var yyyy = today.getFullYear();
    var hh = String(today.getHours()).padStart(2, '0');
    var min = String(today.getMinutes()).padStart(2, '0');
    var ampm = today.getHours() >= 12 ? 'PM' : 'AM';
    var hours12 = today.getHours() % 12 || 12;
    var formattedTime = String(hours12).padStart(2, '0') + ':' + min + ' ' + ampm;
    var formattedToday = dd + '-' + mm + '-' + yyyy + ' ' + formattedTime;

    $('#exit_datetime').val(formattedToday);
    
    // Check which datepicker library is available and use it
    if ($.fn.datetimepicker) {
        $('#exit_datetime').datetimepicker({
            format: 'DD-MM-YYYY hh:mm A',
            defaultDate: today,
            maxDate: today,
            minDate: moment().subtract(1, 'year'),
            useCurrent: true,
            sideBySide: true,
            toolbarPlacement: 'top',
            keepOpen: false,
            showClose: true,
            icons: {
                time: 'glyphicon glyphicon-time',
                date: 'glyphicon glyphicon-calendar',
                up: 'glyphicon glyphicon-chevron-up',
                down: 'glyphicon glyphicon-chevron-down',
                previous: 'glyphicon glyphicon-chevron-left',
                next: 'glyphicon glyphicon-chevron-right',
                today: 'glyphicon glyphicon-screenshot',
                clear: 'glyphicon glyphicon-trash',
                close: 'glyphicon glyphicon-remove'
            },
            widgetPositioning: {
                horizontal: 'auto',
                vertical: 'bottom'
            }
        }).on('dp.show', function() {
            // Add custom close button to the top-right
            setTimeout(function() {
                // Remove the default close button from toolbar if it exists
                $('.bootstrap-datetimepicker-widget .picker-switch td span.glyphicon-remove').parent().remove();
                
                // Add custom close button if it doesn't exist already
                if ($('.bootstrap-datetimepicker-widget .custom-close-btn').length === 0) {
                    var closeBtn = $('<a>', {
                        href: 'javascript:void(0)',
                        class: 'custom-close-btn',
                        style: 'position: absolute; top: 5px; right: 10px; z-index: 1000; color: #333;',
                        html: '<span class="glyphicon glyphicon-remove"></span>'
                    });
                    
                    closeBtn.on('click', function() {
                        $('#exit_datetime').data('DateTimePicker').hide();
                    });
                    
                    $('.bootstrap-datetimepicker-widget').append(closeBtn);
                }
                
                // Remove extra vertical spacing at the top
                $('.bootstrap-datetimepicker-widget .datepicker-days thead tr:first-child').css('display', 'none');
                $('.bootstrap-datetimepicker-widget .datepicker-days thead tr.picker-switch').css('padding-top', '0');
                $('.bootstrap-datetimepicker-widget .datepicker-days thead').css('padding-top', '0');
                $('.bootstrap-datetimepicker-widget .datepicker-days').css('padding-top', '0');
                $('.bootstrap-datetimepicker-widget .picker-switch').css('margin-top', '0');
            }, 0);
        }).on('dp.error', function(e) {
            console.error('Datetimepicker error:', e);
        }).on('dp.change', function(e) {
            // Reset section, student and pickup-by when date changes
            $('#class_section_id').val('');
            $('#student_id').html('<option value="">Select Student</option>').prop('disabled', true);
            $('#pickup_by').val('');
            $('#other_details_section').hide();
            if ($('#parent_info_div').length) {
                $('#parent_info_div').remove();
            }
        });
        
        // Add manual outside click handler
        $(document).on('mousedown', function(e) {
            // If the target of the click isn't the container nor a descendant of the container
            if (!$(e.target).closest('.bootstrap-datetimepicker-widget').length && 
                !$(e.target).closest('#exit_datetime').length && 
                !$(e.target).closest('.input-group-addon').length) {
                
                var picker = $('#exit_datetime').data('DateTimePicker');
                if (picker && picker.hide) {
                    picker.hide();
                }
            }
        });
        
    } else {
        console.error('Datetimepicker library not found');
    }
    
    // When section is selected, load present students
    $('#class_section_id').change(function() {
        var class_section_id = $(this).val();
        if (class_section_id) {
            loadPresentStudents(class_section_id);
            // Reset student and pickup-by when section changes
            $('#student_id').html('<option value="">Select Student</option>').prop('disabled', true);
            $('#pickup_by').val('');
            $('#other_details_section').hide();
            if ($('#parent_info_div').length) {
                $('#parent_info_div').remove();
            }
        } else {
            $('#student_id').html('<option value="">Select Student</option>').prop('disabled', true);
        }
    });
    
    // Reset pickup-by when student changes
    $('#student_id').change(function() {
        $('#pickup_by').val('');
        $('#other_details_section').hide();
        if ($('#parent_info_div').length) {
            $('#parent_info_div').remove();
        }

        // Reset parent selection for OTP (only if OTP is enabled)
        if (otpEnabled) {
            $('input[name="otp_to"]').prop('checked', false).prop('disabled', false);
            $('.parent-card').removeClass('selected');
            $('#father_option, #mother_option').hide();
            $('#otp_complete_section').hide();
            $('#otp_verification_ui').hide();
            $('#final_submit_btn').prop('disabled', true); // Disable submit button
        }
        $('#remarks').val('');
    });
    
    // Show/hide other name field based on pickup by selection
    $('#pickup_by').change(function() {
        if ($('#student_id').val() == '') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please select a student first',
                timer: 3000,
                showConfirmButton: false
            });
            $(this).val('');
            return;
        }
        
        // Handle Other option - show additional fields
        if ($(this).val() == 'Other') {
            $('#other_details_section').show();
            $('#pickup_other_name').prop('required', true);
            $('#pickup_photo').prop('required', true);
        } else {
            $('#other_details_section').hide();
            $('#pickup_other_name').prop('required', false);
            $('#pickup_photo').prop('required', false);
            // Remove character count display if it exists
            $('.char-count').remove();
        }

        // Show OTP section for ALL pickup options (Self, Father, Mother, Other) - only if OTP is enabled
        if ($(this).val() == 'Self' || $(this).val() == 'Father' || $(this).val() == 'Mother' || $(this).val() == 'Other') {
                var student_id = $('#student_id').val();
                var pickup_by = $(this).val();

                if (student_id) {
                    if (otpEnabled) {
                        // Load both parents for OTP selection and show OTP complete section
                        loadParentsForOTP(student_id);
                        $('#otp_complete_section').show();
                    } else {
                        // No OTP required - enable submit button directly
                        $('#final_submit_btn').prop('disabled', false);
                    }

                    $.ajax({
                        url: "<?php echo site_url('attendance_day_v2/Attendance_day_v2/get_parent_info'); ?>",
                        type: "POST",
                        data: {
                            student_id: student_id,
                            pickup_by: pickup_by
                        },
                        dataType: "json",
                        success: function(data) {
                            if (data.success) {
                                // Always remove existing div to ensure label is updated
                                if ($('#parent_info_div').length) {
                                    $('#parent_info_div').remove();
                                }

                                // Create new div with updated content including photo
                                var defaultImage = pickup_by === 'Mother' ?
                                    'https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/sample_girl_image.png' :
                                    'https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Admission process/student_image.jpg';

                                var parentInfoHtml = `
                                    <div id="parent_info_div" class="alert alert-info">
                                        <div class="parent-info-container">
                                            <div class="photo-container">
                                                <div class="photo-loader">
                                                    <div class="spinner"></div>
                                                </div>
                                                <img src="${data.parent_photo || defaultImage}"
                                                     alt="${pickup_by} Photo"
                                                     class="parent-photo"
                                                     onload="this.classList.add('loaded'); this.parentElement.querySelector('.photo-loader').style.display='none';"
                                                     onerror="this.onerror=null; this.src='${defaultImage}';">
                                            </div>
                                            <div class="parent-details">
                                                <div class="parent-label">${pickup_by} Information</div>
                                                <div class="parent-name">${data.parent_name}</div>
                                                <div class="parent-contact">${data.parent_mobile || 'Contact number not available'}</div>
                                            </div>
                                        </div>
                                    </div>`;

                                $(parentInfoHtml).insertAfter('#pickup_by');
                            } else {
                                if ($('#parent_info_div').length) {
                                    $('#parent_info_div').remove();
                                }
                            }
                        }
                    });
                } else {
                    alert('Please select a student first');
                    $(this).val('');
                }
        } else {
            // Hide all sections when no pickup option is selected
            if (otpEnabled) {
                $('#otp_complete_section').hide();
                $('#final_submit_btn').prop('disabled', true); // Disable submit button
            }
            if ($('#parent_info_div').length) {
                $('#parent_info_div').remove();
            }
        }
    });
    
    // Add character count functionality for Other Name field
    $('#pickup_other_name').on('input', function() {
        var maxLength = 50;
        var currentLength = $(this).val().length;
        var remaining = maxLength - currentLength;
        
        // Remove existing character count display
        $('.char-count').remove();
        
        // Add character count display
        var charCountHtml = '<small class="char-count text-muted" style="display: block; margin-top: 5px; font-size: 12px;">' +
                           '<i class="fa fa-info-circle"></i> ' + remaining + ' characters remaining</small>';
        
        $(this).parent().append(charCountHtml);
        
        // Change color based on remaining characters
        if (remaining <= 10) {
            $('.char-count').removeClass('text-muted').addClass('text-warning');
        } else if (remaining <= 0) {
            $('.char-count').removeClass('text-muted text-warning').addClass('text-danger');
        } else {
            $('.char-count').removeClass('text-warning text-danger').addClass('text-muted');
        }
    });
    


    // Function to load both parents for OTP selection
    function loadParentsForOTP(student_id) {
        $.ajax({
            url: "<?php echo site_url('attendance_day_v2/Attendance_day_v2/get_both_parents_info'); ?>",
            type: "POST",
            data: { student_id: student_id },
            dataType: "json",
            success: function(data) {
                // Reset parent options
                $('#father_option, #mother_option').hide();
                $('input[name="otp_to"]').prop('checked', false);

                if (data.father) {
                    $('#father_name').text((data.father.name || 'Father') + ' (Father)');
                    $('#otp_to_father').attr('data-parent-id', data.father.parent_id || ''); // Store parent_id

                    if (data.father.mobile && data.father.mobile.trim() !== '') {
                        $('#father_mobile').text(data.father.mobile);
                        $('#father_option').show();
                    } else {
                        $('#father_mobile').html('<span style="color: #D93E39; font-size: 12px;">Mobile number not added. Kindly update the mobile number.</span>');
                        $('#father_option').show();
                        // Disable the father option since no mobile
                        $('#otp_to_father').prop('disabled', true);
                    }
                }

                if (data.mother) {
                    $('#mother_name').text((data.mother.name || 'Mother') + ' (Mother)');
                    $('#otp_to_mother').attr('data-parent-id', data.mother.parent_id || ''); // Store parent_id

                    if (data.mother.mobile && data.mother.mobile.trim() !== '') {
                        $('#mother_mobile').text(data.mother.mobile);
                        $('#mother_option').show();
                    } else {
                        $('#mother_mobile').html('<span style="color: #D93E39; font-size: 12px;">Mobile number not added. Kindly update the mobile number.</span>');
                        $('#mother_option').show();
                        // Disable the mother option since no mobile
                        $('#otp_to_mother').prop('disabled', true);
                    }
                }
            },
            error: function() {
                console.log('Error loading parent information for OTP');
            }
        });
    }

    // Function to clean and format mobile number
    function cleanMobileNumber(mobile) {
        // Remove all non-digit characters
        let cleaned = mobile.replace(/\D/g, '');

        // Remove country code if present (assuming +91 for India or other common codes)
        if (cleaned.length > 10) {
            // Remove common country codes
            if (cleaned.startsWith('91') && cleaned.length === 12) {
                cleaned = cleaned.substring(2);
            } else if (cleaned.startsWith('1') && cleaned.length === 11) {
                cleaned = cleaned.substring(1);
            } else if (cleaned.length > 10) {
                // Take last 10 digits
                cleaned = cleaned.slice(-10);
            }
        }

        return cleaned;
    }

    // Auto-format mobile number on input
    $('#staff_mobile').on('input', function() {
        const cleaned = cleanMobileNumber($(this).val());
        $(this).val(cleaned);
    });

    // Function to send OTP (reusable for both send and resend)
    function sendOTP() {
        const selectedParent = $('input[name="otp_to"]:checked').val();
        const selectedParentElement = $('input[name="otp_to"]:checked');

        if (!selectedParent) {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please select a parent to send OTP to',
                timer: 3000,
                showConfirmButton: false
            });
            return;
        }

        // Get parent details based on selection
        let parentName, parentMobile, parentId;
        if (selectedParent === 'Father') {
            parentName = $('#father_name').text();
            parentMobile = cleanMobileNumber($('#father_mobile').text());
            parentId = $('#otp_to_father').attr('data-parent-id');
        } else {
            parentName = $('#mother_name').text();
            parentMobile = cleanMobileNumber($('#mother_mobile').text());
            parentId = $('#otp_to_mother').attr('data-parent-id');
        }

        if (!parentMobile || parentMobile === 'Mobile not available' || parentMobile.length < 8) {
            Swal.fire({
                icon: 'error',
                title: 'Mobile Number Missing!',
                text: 'Mobile number is not added for the selected parent. Kindly update the mobile number in parent profile.',
                timer: 5000,
                showConfirmButton: true
            });
            return;
        }

        // Validate mobile number format
        const mobileRegex = /^[0-9]{8,15}$/;
        if (!mobileRegex.test(parentMobile)) {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Invalid mobile number format',
                timer: 3000,
                showConfirmButton: false
            });
            return;
        }

        // Disable buttons and show loading
        // $('#send_otp_btn').prop('disabled', true).text('Sending...');
        // $('#resend_otp_btn').prop('disabled', true).text('Resending...');

        $.ajax({
            url: '<?php echo site_url("attendance_day_v2/Attendance_day_v2/send_emergency_exit_otp"); ?>',
            type: 'POST',
            data: {
                mobileNumber: parentMobile,
                name: parentName,
                parent_id: parentId // Include parent_id for proper tracking
            },
            success: function(response) {
                const res = JSON.parse(response);
                if (res.status === 'ok') {
                    $('#otp_input_div').show();
                    $('#verify_otp_btn').show();
                    $('#send_otp_btn').hide();

                    // Disable parent selection after OTP is sent
                    $('input[name="otp_to"]').prop('disabled', true);

                    // Show resend button only if it's not already visible (first time)
                    if (!$('#resend_otp_btn').is(':visible')) {
                        $('#resend_otp_btn').show().prop('disabled', false).text('Resend OTP');
                    }
                    // If resend button is already visible, don't reset its timer state

                    // Clear any previous OTP input
                    $('#otp_code').val('');

                    Swal.fire({
                        icon: 'success',
                        title: 'OTP Sent!',
                        text: res.msg,
                        timer: 3000,
                        showConfirmButton: false
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: res.msg,
                        timer: 3000,
                        showConfirmButton: false
                    });
                    // Reset button states on error
                    if ($('#send_otp_btn').is(':visible')) {
                        $('#send_otp_btn').prop('disabled', false).text('Send OTP');
                    } else {
                        $('#resend_otp_btn').prop('disabled', false).text('Resend OTP');
                    }
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Failed to send OTP. Please try again.',
                    timer: 3000,
                    showConfirmButton: false
                });
                // Reset button states on error
                if ($('#send_otp_btn').is(':visible')) {
                    $('#send_otp_btn').prop('disabled', false).text('Send OTP');
                } else {
                    $('#resend_otp_btn').prop('disabled', false).text('Resend OTP');
                }
            }
        });
    }

    // Send OTP button click
    $('#send_otp_btn').click(function() {
        sendOTP();
    });

    // Resend OTP button click with timer
    $('#resend_otp_btn').click(function() {
        // Start resend timer (30 seconds)
        let countdown = 30;
        $('#resend_otp_btn').prop('disabled', true).text(`Resend OTP (${countdown}s)`);

        const timer = setInterval(function() {
            countdown--;
            if (countdown > 0) {
                $('#resend_otp_btn').text(`Resend OTP (${countdown}s)`);
            } else {
                clearInterval(timer);
                $('#resend_otp_btn').prop('disabled', false).text('Resend OTP');
            }
        }, 1000);

        sendOTP();
    });

    // Verify OTP button click
    $('#verify_otp_btn').click(function() {
        const selectedParent = $('input[name="otp_to"]:checked').val();
        const otp = $('#otp_code').val();

        // Get parent details based on selection
        let parentName, parentMobile, parentId;
        if (selectedParent === 'Father') {
            parentName = $('#father_name').text();
            parentMobile = cleanMobileNumber($('#father_mobile').text());
            parentId = $('#otp_to_father').attr('data-parent-id');
        } else {
            parentName = $('#mother_name').text();
            parentMobile = cleanMobileNumber($('#mother_mobile').text());
            parentId = $('#otp_to_mother').attr('data-parent-id');
        }

        if (!otp || otp.length !== 6) {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please enter a valid 6-digit OTP',
                timer: 3000,
                showConfirmButton: false
            });
            return;
        }

        // Disable button and show loading
        $('#verify_otp_btn').prop('disabled', true).text('Verifying...');

        $.ajax({
            url: '<?php echo site_url("attendance_day_v2/Attendance_day_v2/verify_emergency_exit_otp"); ?>',
            type: 'POST',
            data: {
                mobileNumber: parentMobile,
                name: parentName,
                otpCode: otp,
                parent_id: parentId // Include parent_id for proper verification
            },
            success: function(response) {
                const res = JSON.parse(response);
                if (res.status === 'ok') {
                    Swal.fire({
                        icon: 'success',
                        title: 'OTP Verified!',
                        text: 'Authentication successful. Please enter remarks to complete the emergency exit.',
                        timer: 3000,
                        showConfirmButton: false
                    });

                    // Hide OTP verification UI
                    $('#otp_verification_ui').hide();

                    // Hide mobile number display and show verification status
                    const selectedParent = $('input[name="otp_to"]:checked').val();
                    if (selectedParent === 'Father') {
                        $('#father_mobile').html('<span style="color: #3DA755; font-weight: bold; font-size: 12px;"><i class="fa fa-check-circle"></i> OTP Verified</span>');
                    } else {
                        $('#mother_mobile').html('<span style="color: #3DA755; font-weight: bold; font-size: 12px;"><i class="fa fa-check-circle"></i> OTP Verified</span>');
                    }

                    // Disable parent selection after verification
                    $('input[name="otp_to"]').prop('disabled', true);

                    // Enable submit button
                    $('#final_submit_btn').prop('disabled', false);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: res.msg,
                        timer: 3000,
                        showConfirmButton: false
                    });
                    $('#verify_otp_btn').prop('disabled', false).text('Verify OTP');
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Failed to verify OTP. Please try again.',
                    timer: 3000,
                    showConfirmButton: false
                });
                $('#verify_otp_btn').prop('disabled', false).text('Verify OTP');
            }
        });
    });

    // Parent selection radio button change
    $(document).on('change', 'input[name="otp_to"]', function() {
        // Check if this parent option is disabled (no mobile number)
        if ($(this).prop('disabled')) {
            $(this).prop('checked', false);
            Swal.fire({
                icon: 'warning',
                title: 'Mobile Number Required!',
                text: 'This parent does not have a mobile number added. Please update the mobile number in parent profile.',
                timer: 4000,
                showConfirmButton: true
            });
            return;
        }

        // Remove selected class from all cards
        $('.parent-card').removeClass('selected');

        // Add selected class to the selected card
        if ($(this).is(':checked')) {
            $(this).next('.parent-card').addClass('selected');

            // Show OTP verification UI immediately
            $('#otp_verification_ui').show();
        }
    });

    // Cancel OTP button click
    $('#cancel_otp_btn').click(function() {
        // Hide OTP verification UI
        $('#otp_verification_ui').hide();
        $('#final_submit_btn').prop('disabled', true); // Disable submit button

        // Reset parent selection
        $('input[name="otp_to"]').prop('checked', false).prop('disabled', false);
        $('.parent-card').removeClass('selected');
        $('#father_option, #mother_option').hide();

        // Reset mobile number display (reload parent info to restore original mobile numbers)
        const studentId = $('#student_id').val();
        if (studentId) {
            loadParentsForOTP(studentId);
        }

        // Show OTP complete section again for any pickup_by option (only if OTP enabled)
        if (otpEnabled) {
            const pickupBy = $('#pickup_by').val();
            if (pickupBy === 'Self' || pickupBy === 'Father' || pickupBy === 'Mother' || pickupBy === 'Other') {
                $('#otp_complete_section').show();
            }
        }

        // Reset OTP form
        $('#otp_code').val('');
        $('#otp_input_div').hide();
        $('#send_otp_btn').show().prop('disabled', false).text('Send OTP');
        $('#verify_otp_btn').hide().prop('disabled', false).text('Verify OTP');
        $('#resend_otp_btn').hide().prop('disabled', false).text('Resend OTP');

        // Clear remarks
        $('#remarks').val('');
    });

    // Form validation function
    function validateFormFields() {
        // Validate exit datetime
        if ($('#exit_datetime').val() == '') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please select exit date and time',
                timer: 3000,
                showConfirmButton: false
            });
            return false;
        }

        // Validate section
        if ($('#class_section_id').val() == '') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please select a section',
                timer: 3000,
                showConfirmButton: false
            });
            return false;
        }

        // Validate student
        if ($('#student_id').val() == '') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please select a student',
                timer: 3000,
                showConfirmButton: false
            });
            return false;
        }

        // Validate pickup by
        if ($('#pickup_by').val() == '') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please select who is picking up the student',
                timer: 3000,
                showConfirmButton: false
            });
            return false;
        }

        // Validate other name if Other is selected
        if ($('#pickup_by').val() == 'Other' && $('#pickup_other_name').val() == '') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please enter the name of the person picking up the student',
                timer: 3000,
                showConfirmButton: false
            });
            return false;
        }

        // Validate other name length if Other is selected
        if ($('#pickup_by').val() == 'Other' && $('#pickup_other_name').val().length > 50) {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Other name cannot exceed 50 characters',
                timer: 3000,
                showConfirmButton: false
            });
            return false;
        }

        // Validate photo if Other is selected
        if ($('#pickup_by').val() == 'Other') {
            const photoUrl = $('#uploaded_photo_url').val();

            if (!photoUrl) {
                Swal.fire({
                    icon: 'error',
                    title: 'Photo Required',
                    text: 'Please upload a photo of the person picking up the student',
                    timer: 3000,
                    showConfirmButton: false
                });
                return false;
            }
        }



        // Validate remarks
        if ($('#remarks').val() == '') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Please enter remarks for this emergency exit',
                timer: 3000,
                showConfirmButton: false
            });
            return false;
        }

        return true;
    }

    // Form submission
    $('#emergency_exit_form').submit(function(e) {
        // Prevent default form submission
        e.preventDefault();

        // Validate all required fields
        if (!validateFormFields()) {
            return false;
        }

        // Handle parent details based on OTP configuration
        if (otpEnabled) {
            // OTP flow - get selected parent details (required for all pickup options)
            const selectedParent = $('input[name="otp_to"]:checked').val();

            if (!selectedParent) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Please select a parent for OTP verification',
                    timer: 3000,
                    showConfirmButton: false
                });
                return false;
            }

            let parentName, parentMobile, parentId;

            if (selectedParent === 'Father') {
                parentName = $('#father_name').text();
                parentMobile = $('#father_mobile').text();
                parentId = $('#otp_to_father').attr('data-parent-id');
            } else {
                parentName = $('#mother_name').text();
                parentMobile = $('#mother_mobile').text();
                parentId = $('#otp_to_mother').attr('data-parent-id');
            }

            // Add parent details to form data
            $('<input>').attr({
                type: 'hidden',
                name: 'authorized_by_parent_id',
                value: parentId
            }).appendTo('#emergency_exit_form');

            $('<input>').attr({
                type: 'hidden',
                name: 'authorized_by_mobile',
                value: parentMobile
            }).appendTo('#emergency_exit_form');
        }
        // For non-OTP flow, no parent details are required

        // Submit the form
        this.submit();
    });
    
    // Function to load present students
    function loadPresentStudents(class_section_id) {
        // Validate input
        if (!class_section_id) {
            console.error("loadPresentStudents: class_section_id is required");
            $('#student_id').html('<option value="">Select Student</option>').prop('disabled', true);
            return;
        }

        // Extract just the date part from the datetime value
        var exitDate = $('#exit_datetime').val().split(' ')[0];
        var formattedDate = moment(exitDate, 'DD-MM-YYYY').format('YYYY-MM-DD');

        console.log("Loading students for class_section_id:", class_section_id, "date:", formattedDate);

        $.ajax({
            url: "<?php echo site_url('attendance_day_v2/Attendance_day_v2/get_present_students'); ?>",
            type: "POST",
            data: {
                class_section_id: class_section_id,
                date: formattedDate
            },
            dataType: "json",
            success: function(data) {
                console.log("Received students data:", data);

                var options = '<option value="">Select Student</option>';
                if (data.length > 0) {
                    $.each(data, function(index, student) {
                        options += '<option value="' + student.student_admission_id + '">' + student.student_name + '</option>';
                    });
                    $('#student_id').html(options).prop('disabled', false);
                    console.log("Loaded", data.length, "students for section", class_section_id);
                } else {
                    $('#student_id').html('<option value="">No present students found</option>').prop('disabled', true);
                    console.log("No students found for section", class_section_id, "on date", formattedDate);
                }
            },
            error: function(xhr, status, error) {
                console.error("Error loading students:", error);
                console.error("Response:", xhr.responseText);
                $('#student_id').html('<option value="">Error loading students</option>').prop('disabled', true);
            }
        });
    }

    // Handle photo upload
    function handlePhotoUpload(input) {
        
        if (input.files && input.files[0]) {
            const file = input.files[0];
            
            // Validate file type
            if (!file.type.match('image.*')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid File',
                    text: 'Please select an image file',
                    timer: 3000,
                    showConfirmButton: false
                });
                input.value = '';
                return;
            }
            
            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                Swal.fire({
                    icon: 'error',
                    title: 'File Too Large',
                    text: 'Please select an image smaller than 5MB',
                    timer: 3000,
                    showConfirmButton: false
                });
                input.value = '';
                return;
            }
            
            // Show preview immediately
            const reader = new FileReader();
            
            reader.onloadstart = function() {
                console.log('Starting to read file');
            };
            
            reader.onload = function(e) {
                const previewContainer = $('#photo_preview_container');
                const previewImage = $('#photo_preview');
                
                // Set the image source
                previewImage.attr('src', e.target.result);
                
                // Show the container and remove button
                previewContainer.show();
                $('.remove-photo').show();
                $('.custom-file-upload').html('<i class="fa fa-camera"></i> Change Photo');
            };
            
            reader.onerror = function(error) {
                console.error('Error reading file:', error);
            };
            
            // Start reading the file
            reader.readAsDataURL(file);
            
            // Upload to Wasabi
            uploadToWasabi(file);
        }
    }

    // Upload to Wasabi
    function uploadToWasabi(file) {
        return new Promise(function(resolve, reject) {
            try {
                $.ajax({
                    url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
                    type: 'post',
                    data: {'filename':file.name, 'file_type':file.type, 'folder':'emergency_exit'},
                    success: function(response) {
                        // Show progress bar
                        $('#upload_progress').show();
                        
                        response = JSON.parse(response);
                        var path = response.path;
                        var signedUrl = response.signedUrl;

                        $.ajax({
                            url: signedUrl,
                            type: 'PUT',
                            headers: {
                                "Content-Type": file.type, 
                                "x-amz-acl":"public-read" 
                            },
                            processData: false,
                            data: file,
                            xhr: function () {
                                var xhr = $.ajaxSettings.xhr();
                                xhr.upload.onprogress = function (e) {
                                    if (e.lengthComputable) {
                                        const percent = Math.round((e.loaded / e.total) * 100);
                                        $('#upload_progress_bar').css('width', percent + '%');
                                        $('#upload_percentage').text(percent + '%');
                                    }
                                };
                                return xhr;
                            },
                            success: function(response) {
                                $('#uploaded_photo_url').val(path);
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Upload Complete',
                                    text: 'Photo uploaded successfully',
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                                resolve({path:path, name:file.name, type:file.type});
                            },
                            error: function(err) {
                                console.error('Upload error:', err);
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Upload Failed',
                                    text: 'Failed to upload photo. Please try again.',
                                    timer: 3000,
                                    showConfirmButton: false
                                });
                                reject(err);
                            },
                            complete: function() {
                                // Hide progress bar after a short delay
                                setTimeout(() => {
                                    $('#upload_progress').hide();
                                }, 1000);
                            }
                        });
                    },
                    error: function (err) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Upload Failed',
                            text: 'Failed to get upload URL. Please try again.',
                            timer: 3000,
                            showConfirmButton: false
                        });
                        reject(err);
                    }
                });
            } catch(err) {
                console.error('Error:', err);
                Swal.fire({
                    icon: 'error',
                    title: 'Upload Failed',
                    text: 'An unexpected error occurred. Please try again.',
                    timer: 3000,
                    showConfirmButton: false
                });
                reject(err);
            }
        });
    }

    // Remove photo
    function removePhoto() {
        $('#pickup_photo').val('');
        $('#photo_preview').attr('src', '');
        $('#photo_preview_container').hide();
        $('#uploaded_photo_url').val('');
        $('.remove-photo').hide();
        $('.custom-file-upload').html('<i class="fa fa-camera"></i> Choose Photo');
    }

    // Make functions globally available
    window.handlePhotoUpload = handlePhotoUpload;
    window.removePhoto = removePhoto;
});
</script>










