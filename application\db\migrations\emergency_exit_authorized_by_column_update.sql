-- Migration to update emergency_exit_authorized_by_name column to emergency_exit_authorized_by
-- This changes the column to store parent_id (avatar_id) instead of parent name

-- Step 1: Add new column emergency_exit_authorized_by to store parent_id
ALTER TABLE `attendance_std_day_v2_students` 
ADD COLUMN `emergency_exit_authorized_by` INT(11) NULL DEFAULT NULL 
AFTER `emergency_exit_allowed_by`;

-- Step 2: Update existing data to populate parent_id based on parent name and mobile
-- This query attempts to match existing parent names with their corresponding avatar_ids
UPDATE `attendance_std_day_v2_students` asdvs
LEFT JOIN `student_relation` sr ON sr.std_id = asdvs.student_admission_id
LEFT JOIN `parent` p ON p.id = sr.relation_id
LEFT JOIN `avatar` a ON a.stakeholder_id = p.id AND a.avatar_type = 2
SET asdvs.emergency_exit_authorized_by = a.id
WHERE asdvs.is_emergency_exit = 1 
  AND asdvs.emergency_exit_authorized_by_name IS NOT NULL 
  AND asdvs.emergency_exit_authorized_by_name != ''
  AND (
    (sr.relation_type = 'Father' AND asdvs.emergency_exit_authorized_by_name LIKE CONCAT('%', p.first_name, '%'))
    OR 
    (sr.relation_type = 'Mother' AND asdvs.emergency_exit_authorized_by_name LIKE CONCAT('%', p.first_name, '%'))
  )
  AND p.mobile_no = asdvs.emergency_exit_authorized_by_mobile;

-- Step 3: Drop the old column emergency_exit_authorized_by_name
ALTER TABLE `attendance_std_day_v2_students` 
DROP COLUMN `emergency_exit_authorized_by_name`;

-- Step 4: Add foreign key constraint (optional - uncomment if you want referential integrity)
-- ALTER TABLE `attendance_std_day_v2_students` 
-- ADD CONSTRAINT `fk_emergency_exit_authorized_by` 
-- FOREIGN KEY (`emergency_exit_authorized_by`) REFERENCES `avatar`(`id`) 
-- ON DELETE SET NULL ON UPDATE CASCADE;
